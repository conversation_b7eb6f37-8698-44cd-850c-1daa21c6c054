核心布局要求

16:9响应式画布布局，默认占满视口90%区域

三级树状拓扑结构（中心节点→5大课程类别→具体课程）

智能间距算法：子节点自动等距放射状排列

视觉风格规范

主色系：科技蓝渐变 (#2A3FCD → #1A73E8) + 中性灰

节点样式：

中心节点：直径120px的发光圆环效果

二级节点：六边形宝石样式 80x80px

三级节点：圆角矩形卡片 60x40px

动态连线：带流动光点的贝塞尔曲线

字体：Google Sans为主字体，加粗层级标题

交互功能需求

双击节点展开/折叠子分支

鼠标滚轮平滑缩放（0.5x-2x范围）

节点hover时：

显示半透明背景光晕

弹出课程详情tooltip（含学分/课时信息）

拖拽画布功能（限制移动边界）

动画效果

初始加载时的粒子聚合动画

分支展开时的缓动弹性效果

状态切换时的渐隐过渡（300ms）

技术要求

使用D3.js + GSAP实现动态布局

CSS变量控制主题色系

移动端触控事件兼容

验收标准

在1920x1080分辨率下完美呈现

50+课程节点时仍保持流畅

打印模式下的矢量输出支持

建议开发顺序：

搭建基础力导向图框架

实现自定义节点渲染

添加交互逻辑

优化动画性能

响应式适配