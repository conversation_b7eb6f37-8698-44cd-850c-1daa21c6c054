<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商学院2025级金融服务与管理专业课程体系</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #ffffff;
            min-height: 100vh;
            color: #333;
        }

        .container {
            width: 100%;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            color: #1a202c;
            padding: 25px 0 15px 0;
            border-bottom: 2px solid #e2e8f0;
        }

        .header h1 {
            font-size: 2.2rem;
            margin-bottom: 0;
            font-weight: 700;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            color: #64748b;
            font-weight: 500;
        }

        .main-content {
            background: #ffffff;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
        }

        .filter-section {
            margin-bottom: 25px;
            padding: 18px;
            background: linear-gradient(135deg, #f6f9fc 0%, #e9ecef 100%);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .filter-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #374151;
            font-size: 14px;
        }

        .filter-group select, .filter-group input {
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .filter-group select:focus, .filter-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn-group {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: #f8fafc;
            color: #1a202c;
            padding: 12px 10px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.12);
            transform: translateY(-1px);
        }

        .stat-card h3 {
            font-size: 1.4rem;
            margin-bottom: 2px;
            color: #4f46e5;
            font-weight: 700;
        }

        .stat-card p {
            color: #64748b;
            font-size: 12px;
            font-weight: 500;
            margin: 0;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            scroll-margin-top: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .table-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            border-bottom: 2px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-header h3 {
            color: #1e293b;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
        }

        .result-info {
            color: #64748b;
            font-size: 14px;
            font-weight: 500;
            margin: 0;
        }

        .result-info span {
            color: #4f46e5;
            font-weight: 700;
            font-size: 16px;
        }

        /* 查询按钮加载状态 */
        .btn-loading {
            position: relative;
            color: transparent !important;
        }

        .btn-loading::after {
            content: "";
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            margin-left: -8px;
            margin-top: -8px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            font-size: 14px;
            z-index: 1000;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .notification-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .notification-info {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        .notification-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .notification-error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        /* 回到顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-to-top.show {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            font-size: 13px;
        }

        th {
            background: #4f46e5;
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        td {
            padding: 12px 10px;
            border-bottom: 1px solid #e5e7eb;
            vertical-align: top;
        }

        tr:hover {
            background-color: #f8fafc;
        }

        .table-btn {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 2px;
        }

        /* 课程类别颜色 */
        .btn-category.cat-public-required {
            background: #fef3c7;
            color: #92400e;
        }

        .btn-category.cat-public-elective {
            background: #ddd6fe;
            color: #5b21b6;
        }

        .btn-category.cat-major-basic {
            background: #d1fae5;
            color: #065f46;
        }

        .btn-category.cat-major-core {
            background: #bfdbfe;
            color: #1e40af;
        }

        .btn-category.cat-major-expand {
            background: #fce7f3;
            color: #be185d;
        }

        /* 模块颜色 */
        .btn-module.mod-ideology {
            background: #fee2e2;
            color: #991b1b;
        }

        .btn-module.mod-sports {
            background: #dcfce7;
            color: #166534;
        }

        .btn-module.mod-military {
            background: #f3f4f6;
            color: #374151;
        }

        .btn-module.mod-psychology {
            background: #e0e7ff;
            color: #3730a3;
        }

        .btn-module.mod-labor {
            background: #fef3c7;
            color: #92400e;
        }

        .btn-module.mod-practice {
            background: #ecfdf5;
            color: #047857;
        }

        .btn-module.mod-language {
            background: #ede9fe;
            color: #6b21a8;
        }

        .btn-module.mod-science {
            background: #dbeafe;
            color: #1e40af;
        }

        .btn-module.mod-information {
            background: #cffafe;
            color: #0f766e;
        }

        .btn-module.mod-law {
            background: #fed7c7;
            color: #c2410c;
        }

        .btn-module.mod-health {
            background: #fdf2f8;
            color: #be185d;
        }

        .btn-module.mod-career {
            background: #f0fdf4;
            color: #15803d;
        }

        .btn-module.mod-major {
            background: #eff6ff;
            color: #1d4ed8;
        }

        .btn-course-name {
            background: #dcfce7;
            color: #166534;
            font-weight: 600;
        }

        /* 考核方式颜色 */
        .btn-exam.exam-test {
            background: #fee2e2;
            color: #991b1b;
        }

        .btn-exam.exam-check {
            background: #dcfce7;
            color: #166534;
        }

        .table-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* 课程群颜色标注 */
        .fintech-course {
            border-left: 4px solid #10b981 !important;
            background: rgba(16, 185, 129, 0.05) !important;
        }

        .supply-chain-course {
            border-left: 4px solid #f59e0b !important;
            background: rgba(245, 158, 11, 0.05) !important;
        }

        /* 模块筛选按钮样式 */
        .module-filter-section {
            margin: 16px 0;
            padding: 16px;
            background: #f8fafc;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }

        .module-label {
            font-weight: 600;
            color: #374151;
            font-size: 15px;
            margin-bottom: 12px;
            display: block;
        }

        .module-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .module-btn {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .module-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .module-btn.active {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* 模块按钮颜色 - 与表格模块颜色保持一致 */
        .module-btn.mod-ideology {
            background: #fee2e2;
            color: #991b1b;
            border-color: #fca5a5;
        }

        .module-btn.mod-ideology:hover, .module-btn.mod-ideology.active {
            background: #991b1b;
            color: white;
            border-color: #991b1b;
        }

        .module-btn.mod-sports {
            background: #dcfce7;
            color: #166534;
            border-color: #86efac;
        }

        .module-btn.mod-sports:hover, .module-btn.mod-sports.active {
            background: #166534;
            color: white;
            border-color: #166534;
        }

        .module-btn.mod-military {
            background: #f3f4f6;
            color: #374151;
            border-color: #d1d5db;
        }

        .module-btn.mod-military:hover, .module-btn.mod-military.active {
            background: #374151;
            color: white;
            border-color: #374151;
        }

        .module-btn.mod-psychology {
            background: #e0e7ff;
            color: #3730a3;
            border-color: #a5b4fc;
        }

        .module-btn.mod-psychology:hover, .module-btn.mod-psychology.active {
            background: #3730a3;
            color: white;
            border-color: #3730a3;
        }

        .module-btn.mod-labor {
            background: #fef3c7;
            color: #92400e;
            border-color: #fde68a;
        }

        .module-btn.mod-labor:hover, .module-btn.mod-labor.active {
            background: #92400e;
            color: white;
            border-color: #92400e;
        }

        .module-btn.mod-practice {
            background: #ecfdf5;
            color: #047857;
            border-color: #6ee7b7;
        }

        .module-btn.mod-practice:hover, .module-btn.mod-practice.active {
            background: #047857;
            color: white;
            border-color: #047857;
        }

        .module-btn.mod-language {
            background: #ede9fe;
            color: #6b21a8;
            border-color: #c4b5fd;
        }

        .module-btn.mod-language:hover, .module-btn.mod-language.active {
            background: #6b21a8;
            color: white;
            border-color: #6b21a8;
        }

        .module-btn.mod-science {
            background: #dbeafe;
            color: #1e40af;
            border-color: #93c5fd;
        }

        .module-btn.mod-science:hover, .module-btn.mod-science.active {
            background: #1e40af;
            color: white;
            border-color: #1e40af;
        }

        .module-btn.mod-information {
            background: #cffafe;
            color: #0f766e;
            border-color: #5eead4;
        }

        .module-btn.mod-information:hover, .module-btn.mod-information.active {
            background: #0f766e;
            color: white;
            border-color: #0f766e;
        }

        .module-btn.mod-law {
            background: #fed7c7;
            color: #c2410c;
            border-color: #fdba74;
        }

        .module-btn.mod-law:hover, .module-btn.mod-law.active {
            background: #c2410c;
            color: white;
            border-color: #c2410c;
        }

        .module-btn.mod-health {
            background: #fdf2f8;
            color: #be185d;
            border-color: #f9a8d4;
        }

        .module-btn.mod-health:hover, .module-btn.mod-health.active {
            background: #be185d;
            color: white;
            border-color: #be185d;
        }

        .module-btn.mod-career {
            background: #f0fdf4;
            color: #15803d;
            border-color: #86efac;
        }

        .module-btn.mod-career:hover, .module-btn.mod-career.active {
            background: #15803d;
            color: white;
            border-color: #15803d;
        }

        .module-btn.mod-major {
            background: #eff6ff;
            color: #1d4ed8;
            border-color: #93c5fd;
        }

        .module-btn.mod-major:hover, .module-btn.mod-major.active {
            background: #1d4ed8;
            color: white;
            border-color: #1d4ed8;
        }

        /* 全部按钮保持原有的蓝色主题 */
        .module-btn[data-module=""] {
            background: white;
            color: #374151;
            border-color: #d1d5db;
        }

        .module-btn[data-module=""]:hover, .module-btn[data-module=""].active {
            background: #4f46e5;
            color: white;
            border-color: #4f46e5;
        }

        /* 模块统计信息样式 */
        .module-stats {
            margin-top: 12px;
            padding: 12px;
            background: #f0f9ff;
            border-radius: 6px;
            border: 1px solid #0ea5e9;
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .stat-label {
            font-weight: 600;
            color: #0f172a;
            font-size: 13px;
        }

        .stat-value {
            font-weight: 700;
            color: #0ea5e9;
            font-size: 16px;
        }

        /* 课程群筛选样式 */
        .course-group-filter-section {
            margin: 16px 0;
            padding: 16px;
            background: #f0fdf4;
            border-radius: 10px;
            border: 1px solid #16a34a;
        }

        .course-group-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .course-group-btn {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .course-group-btn:hover {
            transform: translateY(-1px);
        }

        .course-group-btn.active {
            background: #16a34a;
            color: white;
            border-color: #16a34a;
        }

        .fintech-btn:hover {
            border-color: #10b981;
            color: #10b981;
        }

        .fintech-btn.active {
            background: #10b981;
            border-color: #10b981;
        }

        .supply-chain-btn:hover {
            border-color: #f59e0b;
            color: #f59e0b;
        }

        .supply-chain-btn.active {
            background: #f59e0b;
            border-color: #f59e0b;
        }

        .course-groups {
            margin-top: 40px;
        }

        .course-group {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 6px solid #667eea;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }

        .course-group h2 {
            color: #1e293b;
            margin-bottom: 20px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
        }

        .course-group h2::before {
            content: "🚀";
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .course-group p {
            color: #475569;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .course-list {
            display: grid;
            gap: 15px;
        }

        .course-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .course-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left-color: #667eea;
        }

        .course-name {
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .course-name::after {
            content: "▼";
            font-size: 0.8rem;
            color: #667eea;
            transition: transform 0.3s ease;
        }

        .course-name.collapsed::after {
            transform: rotate(-90deg);
        }

        .course-description {
            color: #64748b;
            line-height: 1.6;
            margin-top: 10px;
            padding-left: 15px;
            border-left: 3px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .course-description.hidden {
            display: none;
        }

        .target-position {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-top: 15px;
            font-weight: 600;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .filter-row {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
                margin-bottom: 16px;
            }
            
            .stat-card {
                padding: 10px 6px;
            }
            
            .stat-card h3 {
                font-size: 1.2rem;
            }
            
            .stat-card p {
                font-size: 11px;
            }
            
            .header {
                padding: 20px 0 12px 0;
                margin-bottom: 16px;
            }
            
            .header h1 {
                font-size: 1.8rem;
            }
            
            .filter-section {
                padding: 14px;
                margin-bottom: 20px;
            }
            
            .module-filter-section, .course-group-filter-section {
                padding: 12px;
                margin: 12px 0;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            table {
                font-size: 12px;
            }
            
            th, td {
                padding: 8px 6px;
            }
            
            .module-stats {
                flex-direction: column;
                gap: 10px;
            }
            
            .course-group-buttons, .module-buttons {
                justify-content: center;
            }
            
            .notification {
                right: 10px;
                left: 10px;
                width: auto;
            }
            
            .table-header {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
            
            .back-to-top {
                bottom: 20px;
                right: 20px;
                width: 45px;
                height: 45px;
                font-size: 16px;
            }
            
            .table-container {
                max-height: 60vh;
            }
        }

        /* 思维导图弹窗样式 - 科技蓝主题 */
        :root {
            --primary-blue: #2A3FCD;
            --secondary-blue: #1A73E8;
            --accent-blue: #4285F4;
            --neutral-gray: #5F6368;
            --light-gray: #F8F9FA;
            --dark-gray: #202124;
        }

        .mindmap-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(32, 33, 36, 0.9);
            backdrop-filter: blur(8px);
            align-items: center;
            justify-content: center;
        }

        .mindmap-modal-content {
            background: linear-gradient(135deg, #0F1419 0%, #1A1F2E 100%);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            width: 100vw;
            height: 100vh;
        }

        .mindmap-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 20px 20px 0 0;
            box-shadow: 0 4px 20px rgba(42, 63, 205, 0.4);
        }

        .mindmap-header h2 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .mindmap-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .mindmap-btn {
            padding: 10px 18px;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            font-family: 'Segoe UI', 'Google Sans', sans-serif;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
        }

        .mindmap-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(42, 63, 205, 0.3);
        }

        .mindmap-close {
            background: linear-gradient(135deg, #EA4335 0%, #D33B2C 100%) !important;
            border-color: rgba(234, 67, 53, 0.5) !important;
        }

        .mindmap-close:hover {
            background: linear-gradient(135deg, #D33B2C 0%, #B52D20 100%) !important;
            box-shadow: 0 8px 25px rgba(234, 67, 53, 0.4) !important;
        }

        .mindmap-container {
            flex: 1;
            background: radial-gradient(ellipse at center, rgba(42, 63, 205, 0.1) 0%, rgba(26, 115, 232, 0.05) 50%, transparent 100%);
            overflow: hidden;
            position: relative;
            width: 100%;
            height: 100%;
        }

        #mindmapSvg {
            cursor: grab;
            background: 
                radial-gradient(circle at 25% 25%, rgba(42, 63, 205, 0.1) 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, rgba(26, 115, 232, 0.08) 1px, transparent 1px);
            background-size: 60px 60px, 40px 40px;
            width: 100%;
            height: 100%;
        }

        #mindmapSvg:active {
            cursor: grabbing;
        }

        /* 思维导图节点样式 - 科技感设计 */
        .mindmap-node {
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .mindmap-node:hover {
            transform: scale(1.05);
        }

        .mindmap-node.expanded .expand-indicator {
            transform: rotate(90deg);
        }

        .node-shape {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 中心节点样式 - 无背景 */
        .center-node {
            fill: none;
            stroke: none;
        }

        /* 类别节点样式 - 保留边框 */
        .category-node {
            fill: none;
            stroke-width: 2;
        }
        
        .center-node .node-text {
            fill: #ff6b35;
            font-weight: 900;
            font-size: 20px;
        }
        
        .category-node .node-text {
            fill: #10b981;
            font-weight: 800;
            font-size: 16px;
        }

        /* 课程按钮样式 - 保留圆角矩形 */
        .course-button {
            fill: rgba(248, 249, 250, 0.9);
            stroke: rgba(95, 99, 104, 0.3);
            stroke-width: 1;
            transition: all 0.3s ease;
        }

        .course-button:hover {
            opacity: 0.9;
            stroke-width: 2;
        }

        .node-text {
            font-family: 'Segoe UI', 'Google Sans', sans-serif;
            font-weight: 600;
            text-anchor: middle;
            dominant-baseline: central;
            pointer-events: none;
            letter-spacing: 0.5px;
        }

        .expand-indicator {
            font-family: 'Segoe UI', sans-serif;
            font-size: 16px;
            text-anchor: middle;
            dominant-baseline: central;
            cursor: pointer;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            fill: rgba(255, 255, 255, 0.8);
        }

        .mindmap-line {
            fill: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0.7;
        }

        .mindmap-line:hover {
            opacity: 1;
        }

        .mindmap-line.hidden {
            opacity: 0;
        }



        /* 节点颜色主题 - 科技感设计 */
        
        /* 中心节点 - 纯文字 */
        .center-node .node-shape {
            fill: none;
            stroke: none;
        }

        .center-node .node-text {
            fill: #ff6b35;
            font-size: 20px;
            font-weight: 900;
        }

        /* 类别节点 - 有边框无填充 */
        .category-node .node-shape {
            fill: none;
            stroke-width: 2;
        }

        .category-node .node-text {
            font-size: 16px;
            font-weight: 800;
        }

        /* 课程节点 - 圆角矩形背景 */
        .course-node .node-shape {
            fill: rgba(248, 249, 250, 0.9);
            stroke: rgba(95, 99, 104, 0.3);
            stroke-width: 1;
        }

        .course-node .node-text {
            font-size: 13px;
            font-weight: 600;
        }

        /* 不同类别的边框和文字颜色 */
        .category-public-required .node-shape { 
            stroke: #F59E0B;
        }
        .category-public-required .node-text { 
            fill: #F59E0B;
        }
        .category-public-elective .node-shape { 
            stroke: #8B5CF6;
        }
        .category-public-elective .node-text { 
            fill: #8B5CF6;
        }
        .category-major-basic .node-shape { 
            stroke: #10B981;
        }
        .category-major-basic .node-text { 
            fill: #10B981;
        }
        .category-major-core .node-shape { 
            stroke: #3B82F6;
        }
        .category-major-core .node-text { 
            fill: #3B82F6;
        }
        .category-major-expand .node-shape { 
            stroke: #EC4899;
        }
        .category-major-expand .node-text { 
            fill: #EC4899;
        }

        /* 不同类别课程的文字颜色 */
        .category-public-required .course-node .node-text { fill: #F59E0B !important; }
        .category-public-elective .course-node .node-text { fill: #8B5CF6 !important; }
        .category-major-basic .course-node .node-text { fill: #10B981 !important; }
        .category-major-core .course-node .node-text { fill: #3B82F6 !important; }
        .category-major-expand .course-node .node-text { fill: #EC4899 !important; }

        /* 流动光点动画 */
        @keyframes flowingDots {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: 20; }
        }

        .flowing-line {
            stroke-dasharray: 5 5;
            animation: flowingDots 2s linear infinite;
        }

        /* tooltip样式 */
        .node-tooltip {
            position: absolute;
            background: rgba(32, 33, 36, 0.95);
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 12px;
            font-family: 'Segoe UI', sans-serif;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .node-tooltip.show {
            opacity: 1;
            transform: translateY(0);
        }

        @media (max-width: 768px) {
            .mindmap-modal-content {
                margin: 2vh auto;
                width: 95vw;
                height: 95vh;
                border-radius: 16px;
            }

            .mindmap-header {
                padding: 12px 16px;
                flex-direction: column;
                gap: 12px;
                text-align: center;
            }

            .mindmap-header h2 {
                font-size: 1.2rem;
            }

            .mindmap-controls {
                flex-wrap: wrap;
                justify-content: center;
                gap: 6px;
            }

            .mindmap-btn {
                padding: 8px 14px;
                font-size: 11px;
                border-radius: 8px;
            }

            .node-text {
                font-size: 8px;
            }

            .center-node .node-text {
                font-size: 12px;
            }

            .category-node .node-text {
                font-size: 9px;
            }

            .course-node .node-text {
                font-size: 7px;
            }

            .node-tooltip {
                max-width: 250px;
                font-size: 11px;
                padding: 10px 12px;
            }

            /* 移动端触控优化 */
            #mindmapSvg {
                touch-action: none;
            }

            .mindmap-node {
                cursor: default;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>商学院2025级金融服务与管理专业课程体系</h1>
        </div>

        <div class="main-content">
            <!-- 统计信息 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalCourses">0</h3>
                    <p>课程总数</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalCredits">0</h3>
                    <p>总学分</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalHours">0</h3>
                    <p>总学时</p>
                </div>
                <div class="stat-card">
                    <h3 id="filteredCourses">0</h3>
                    <p>当前显示</p>
                </div>
            </div>

            <!-- 筛选区域 -->
            <div class="filter-section">
                <!-- 第一行：课程类别和课程名称搜索 -->
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="categoryFilter">课程类别</label>
                        <select id="categoryFilter">
                            <option value="">全部类别</option>
                            <option value="公共必修课">公共必修课</option>
                            <option value="公共限选课">公共限选课</option>
                            <option value="专业基础课">专业基础课</option>
                            <option value="专业核心课">专业核心课</option>
                            <option value="专业拓展课">专业拓展课</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="searchInput">课程名称搜索</label>
                        <input type="text" id="searchInput" placeholder="输入课程名称关键词...">
                    </div>
                </div>

                <!-- 第二行：开设学期和课程性质 -->
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="semesterFilter">开设学期</label>
                        <select id="semesterFilter">
                            <option value="">全部学期</option>
                            <option value="1">第一学期</option>
                            <option value="2">第二学期</option>
                            <option value="3">第三学期</option>
                            <option value="4">第四学期</option>
                            <option value="5">第五学期</option>
                            <option value="6">第六学期</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="typeFilter">课程性质</label>
                        <select id="typeFilter">
                            <option value="">全部类型</option>
                            <option value="theory">理论为主</option>
                            <option value="practice">实践为主</option>
                            <option value="mixed">理论实践并重</option>
                        </select>
                    </div>
                </div>
                
                <!-- 模块筛选 - 并列排放 -->
                <div class="module-filter-section">
                    <label class="module-label">课程模块：</label>
                    <div class="module-buttons">
                        <button class="module-btn active" data-module="">全部</button>
                        <button class="module-btn mod-ideology" data-module="思想政治">思想政治</button>
                        <button class="module-btn mod-sports" data-module="体育健康">体育健康</button>
                        <button class="module-btn mod-military" data-module="军事教育">军事教育</button>
                        <button class="module-btn mod-psychology" data-module="心理健康">心理健康</button>
                        <button class="module-btn mod-labor" data-module="劳动教育">劳动教育</button>
                        <button class="module-btn mod-practice" data-module="综合实践">综合实践</button>
                        <button class="module-btn mod-language" data-module="外语素养">外语素养</button>
                        <button class="module-btn mod-science" data-module="自然科学">自然科学</button>
                        <button class="module-btn mod-information" data-module="信息素养">信息素养</button>
                        <button class="module-btn mod-law" data-module="法律法规">法律法规</button>
                        <button class="module-btn mod-health" data-module="健康安全">健康安全</button>
                        <button class="module-btn mod-career" data-module="职业发展">职业发展</button>
                        <button class="module-btn mod-major" data-module="专业基础课">专业基础</button>
                        <button class="module-btn mod-major" data-module="专业核心课">专业核心</button>
                        <button class="module-btn mod-major" data-module="专业拓展课">专业拓展</button>
                    </div>
                    
                    <!-- 模块统计信息 -->
                    <div class="module-stats" id="moduleStats">
                        <div class="stat-item">
                            <span class="stat-label">学时总计：</span>
                            <span class="stat-value" id="moduleHours">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">学分总计：</span>
                            <span class="stat-value" id="moduleCredits">0</span>
                        </div>
                    </div>
                </div>

                <!-- 课程群筛选 -->
                <div class="course-group-filter-section" id="courseGroupSection">
                    <label class="module-label">课程群筛选：</label>
                    <div class="course-group-buttons">
                        <button class="course-group-btn active" data-group="">显示全部</button>
                        <button class="course-group-btn fintech-btn" data-group="fintech">🚀 金融科技课程群</button>
                        <button class="course-group-btn supply-chain-btn" data-group="supply-chain">💼 供应链金融课程群</button>
                    </div>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="searchAndJump()">🔍 筛选查询</button>
                    <button class="btn btn-secondary" onclick="resetFilters()">🔄 重置筛选</button>
                    <button class="btn btn-secondary" onclick="exportData()">📊 导出数据</button>
                    <button class="btn btn-secondary" onclick="generateMindMap()">🧠 生成思维导图</button>
                </div>
            </div>

            <!-- 课程表格 -->
            <div class="table-container" id="tableContainer">
                <div class="table-header">
                    <h3>📊 课程体系</h3>
                    <p class="result-info">共找到 <span id="resultCount">0</span> 门课程</p>
                </div>
                <table id="courseTable">
                    <thead>
                        <tr>
                            <th>课程类别</th>
                            <th>模块</th>
                            <th>课程名称</th>
                            <th>课程代码</th>
                            <th>考核方式</th>
                            <th>学时数</th>
                            <th>理论</th>
                            <th>实践</th>
                            <th>学分</th>
                            <th>第1学期</th>
                            <th>第2学期</th>
                            <th>第3学期</th>
                            <th>第4学期</th>
                            <th>第5学期</th>
                            <th>第6学期</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody id="courseTableBody">
                    </tbody>
                </table>
            </div>

            <!-- 课程群介绍 -->
            <div class="course-groups" id="courseGroupsSection" style="display: none;">
                <div class="course-group" id="fintechGroup" style="display: none;">
                    <h2>金融科技课程群</h2>
                    <p>打造了完整的"金融科技能力课程链"，以技术理解、平台操作与应用能力为导向</p>
                    
                    <div class="course-list">
                        <div class="course-item">
                            <div class="course-name" onclick="toggleCourse(this)">商科人工智能基础</div>
                            <div class="course-description">
                                引导学生理解AI客服、语音识别、模型应用在金融场景中的逻辑。通过实际案例学习，掌握人工智能在金融服务中的应用原理和操作方法。
                            </div>
                        </div>
                        
                        <div class="course-item">
                            <div class="course-name" onclick="toggleCourse(this)">区块链技术</div>
                            <div class="course-description">
                                介绍智能合约、分布式记账、数字货币等金融前沿技术。深入了解区块链在数字支付、供应链金融、数字身份认证等领域的创新应用。
                            </div>
                        </div>
                        
                        <div class="course-item">
                            <div class="course-name" onclick="toggleCourse(this)">金融科技概论</div>
                            <div class="course-description">
                                建立大数据、云计算、FinTech平台运作的整体框架。系统梳理金融科技发展脉络，理解技术变革对传统金融业务的影响和重塑。
                            </div>
                        </div>
                        
                        <div class="course-item">
                            <div class="course-name" onclick="toggleCourse(this)">金融大数据处理</div>
                            <div class="course-description">
                                项目式训练数据采集、清洗、建模与展示。通过真实的金融数据处理项目，培养学生运用大数据技术解决金融业务问题的实战能力。
                            </div>
                        </div>
                        
                        <div class="course-item">
                            <div class="course-name" onclick="toggleCourse(this)">小微企业数字支付与运营</div>
                            <div class="course-description">
                                聚焦小微企业如何借助支付技术提升运营、引流和数字化经营。结合移动支付、电子商务等实际应用场景，培养数字化运营思维。
                            </div>
                        </div>
                    </div>
                    
                    <div class="target-position">
                        🎯 就业方向：金融科技企业、新金融平台类岗位
                    </div>
                </div>

                <div class="course-group" id="supplyChainGroup" style="display: none;">
                    <h2>供应链金融课程群</h2>
                    <p>结合区域小微企业服务需求，课程设计突出"懂企业、懂财务、懂金融"的复合能力</p>
                    
                    <div class="course-list">
                        <div class="course-item">
                            <div class="course-name" onclick="toggleCourse(this)">公司理财</div>
                            <div class="course-description">
                                帮助学生理解企业财务结构、资金成本与融资路径。深入学习企业投融资决策、财务规划、风险管理等核心内容，培养企业财务分析能力。
                            </div>
                        </div>
                        
                        <div class="course-item">
                            <div class="course-name" onclick="toggleCourse(this)">商业银行综合柜面业务</div>
                            <div class="course-description">
                                强化业务办理、风险识别与客户服务实操。通过模拟银行柜面业务场景，训练学生的业务操作技能和客户服务能力。
                            </div>
                        </div>
                        
                        <div class="course-item">
                            <div class="course-name" onclick="toggleCourse(this)">ERP企业经营模拟</div>
                            <div class="course-description">
                                以虚拟企业运行操作促进财务与业务理解联动。通过企业经营沙盘模拟，全面提升学生对企业运营流程和财务管理的理解。
                            </div>
                        </div>
                        
                        <div class="course-item">
                            <div class="course-name" onclick="toggleCourse(this)">供应链金融</div>
                            <div class="course-description">
                                搭建"产-供-销-融"全链条金融认知，结合典型模式如应收账款融资、保理、票据、仓单质押等。培养学生对产业链金融服务的综合理解。
                            </div>
                        </div>
                    </div>
                    
                    <div class="target-position">
                        🎯 就业方向：金融机构企业客户服务专员、小微企业金融顾问
                    </div>
                </div>
            </div>
        </div>

        <!-- 回到顶部按钮 -->
        <button class="back-to-top" id="backToTop" onclick="scrollToTop()">↑</button>

        <!-- 思维导图弹窗 -->
        <div class="mindmap-modal" id="mindmapModal">
            <div class="mindmap-modal-content">
                <div class="mindmap-header">
                    <h2>📊 金融服务与管理专业课程体系思维导图</h2>
                    <div class="mindmap-controls">
                        <button class="mindmap-btn" onclick="zoomIn()">🔍 放大</button>
                        <button class="mindmap-btn" onclick="zoomOut()">🔍 缩小</button>
                        <button class="mindmap-btn" onclick="resetZoom()">⚪ 重置</button>
                        <button class="mindmap-btn" onclick="expandAll()">📂 展开课程</button>
                        <button class="mindmap-btn" onclick="collapseAll()">📁 收起课程</button>
                        <button class="mindmap-btn mindmap-close" onclick="closeMindMap()">✕ 关闭</button>
                    </div>
                </div>
                <div class="mindmap-container" id="mindmapContainer">
                    <svg id="mindmapSvg" width="100%" height="100%">
                        <defs>
                            <!-- 中心节点渐变 -->
                            <radialGradient id="centerGradient" cx="50%" cy="50%" r="50%">
                                <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:1" />
                                <stop offset="50%" style="stop-color:#f7931e;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#ffb347;stop-opacity:0.8" />
                            </radialGradient>
                            
                            <!-- 类别节点渐变 -->
                            <linearGradient id="categoryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.9" />
                                <stop offset="100%" style="stop-color:#059669;stop-opacity:0.8" />
                            </linearGradient>
                            
                            <!-- 发光滤镜 -->
                            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                                <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                                <feMerge> 
                                    <feMergeNode in="coloredBlur"/>
                                    <feMergeNode in="SourceGraphic"/>
                                </feMerge>
                            </filter>
                            
                            <!-- 流动光点图案 -->
                            <pattern id="flowingDots" patternUnits="userSpaceOnUse" width="20" height="2">
                                <circle cx="5" cy="1" r="1" fill="rgba(42, 63, 205, 0.6)">
                                    <animate attributeName="cx" values="5;15;5" dur="2s" repeatCount="indefinite"/>
                                </circle>
                                <circle cx="15" cy="1" r="1" fill="rgba(26, 115, 232, 0.4)">
                                    <animate attributeName="cx" values="15;25;15" dur="2s" repeatCount="indefinite"/>
                                </circle>
                            </pattern>
                            
                            <!-- 箭头标记 -->
                            <marker id="arrowhead" markerWidth="8" markerHeight="6" 
                                    refX="8" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="rgba(42, 63, 205, 0.7)" />
                            </marker>
                        </defs>
                        <g id="mindmapGroup"></g>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <script>

        const courseGroups = {
            fintech: [
                "商科人工智能基础", "区块链技术", "金融科技概论（二）", "金融大数据处理", 
                "小微企业数字支付与运营", "数字信贷", "数字金融业务", "金融大数据处理实训"
            ],
            "supply-chain": [
                "公司理财（二）", "商业银行综合柜台业务实训", "工商企业模拟(ERP)", 
                "供应链金融", "公司理财实训", "银行信贷实训", "EXCEL在财务管理中的应用（二）",
                "财务会计基础", "创业法律基础", "商业文书写作", "个人理财（二）", "金融服务营销（二）"
            ]
        };


        const coursesData = [
            {category: "公共必修课", module: "思想政治", name: "思想道德与法治", code: "11093002", exam: "考试", hours: 48, theory: 40, practice: 8, credits: 3, semester1: "4*12W", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: "", courseGroup: ""},
            {category: "公共必修课", module: "思想政治", name: "毛泽东思想和中国特色社会义理论体系概论", code: "11093004", exam: "考试", hours: 32, theory: 26, practice: 6, credits: 2, semester1: "", semester2: "2*16W", semester3: "", semester4: "", semester5: "", semester6: "", note: "", courseGroup: ""},
            {category: "公共必修课", module: "思想政治", name: "习近平新时代中国特色社会主义思想概论", code: "11093003", exam: "考试", hours: 48, theory: 40, practice: 8, credits: 3, semester1: "", semester2: "", semester3: "4*12W", semester4: "", semester5: "", semester6: "", note: "", courseGroup: ""},
            {category: "公共必修课", module: "思想政治", name: "形势与政策1", code: "1109300101", exam: "考试", hours: 8, theory: 8, practice: 0, credits: "", semester1: "2*4W", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "公共必修课", module: "思想政治", name: "形势与政策2", code: "1109300102", exam: "考试", hours: 8, theory: 8, practice: 0, credits: "", semester1: "", semester2: "2*4W", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "公共必修课", module: "思想政治", name: "形势与政策3", code: "1109300103", exam: "考试", hours: 8, theory: 8, practice: 0, credits: "", semester1: "", semester2: "", semester3: "2*4W", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "公共必修课", module: "思想政治", name: "形势与政策4", code: "1109300104", exam: "考试", hours: 8, theory: 8, practice: 0, credits: "", semester1: "", semester2: "", semester3: "", semester4: "2*4W", semester5: "", semester6: "", note: ""},
            {category: "公共必修课", module: "思想政治", name: "形势与政策5", code: "1109300105", exam: "考试", hours: 8, theory: 8, practice: 0, credits: "", semester1: "", semester2: "", semester3: "", semester4: "", semester5: "2*4W", semester6: "", note: ""},
            {category: "公共必修课", module: "思想政治", name: "形势与政策6", code: "1109300106", exam: "考试", hours: 8, theory: 8, practice: 0, credits: "", semester1: "", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "2*4W", note: ""},
            {category: "公共必修课", module: "体育健康", name: "高职体育Ⅰ", code: "11093005", exam: "考试", hours: 30, theory: 2, practice: 28, credits: 2, semester1: "2*15W", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "公共必修课", module: "体育健康", name: "高职体育Ⅱ", code: "11093006", exam: "考试", hours: 34, theory: 4, practice: 30, credits: 2, semester1: "", semester2: "2*17W", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "公共必修课", module: "体育健康", name: "《国家学生体质健康标准》测试", code: "11093007", exam: "考试", hours: 16, theory: 0, practice: 16, credits: 1, semester1: "", semester2: "", semester3: "", semester4: "4*4W", semester5: "", semester6: "", note: "测试16学时"},
            {category: "公共必修课", module: "军事教育", name: "军事理论", code: "11093011", exam: "考试", hours: 36, theory: 36, practice: 0, credits: 2, semester1: "2*18W", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: "线上30学时"},
            {category: "公共必修课", module: "军事教育", name: "军事技能", code: "11093012", exam: "考试", hours: "", theory: "", practice: "", credits: 2, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: "2周"},
            {category: "公共必修课", module: "心理健康", name: "心理健康教育", code: "11093024", exam: "考试", hours: 32, theory: 32, practice: 0, credits: 2, semester1: "2*16W", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: "线上12学时"},
            {category: "公共必修课", module: "劳动教育", name: "劳动理论教育", code: "11093018", exam: "考试", hours: 16, theory: 16, practice: 0, credits: 1, semester1: "2*8W", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: "线上12学时"},
            {category: "公共必修课", module: "劳动教育", name: "劳动实践教育", code: "11093019", exam: "考试", hours: 32, theory: 0, practice: 32, credits: 1, semester1: "2*4W", semester2: "2*4W", semester3: "2*4W", semester4: "2*4W", semester5: "", semester6: "", note: "实践32学时"},
            {category: "公共必修课", module: "综合实践", name: "综合实践活动Ⅰ", code: "1109302701", exam: "考试", hours: 40, theory: 0, practice: 40, credits: 2, semester1: "40", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "公共必修课", module: "综合实践", name: "综合实践活动Ⅱ", code: "1109302702", exam: "考试", hours: 40, theory: 0, practice: 40, credits: 2, semester1: "", semester2: "40", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "公共必修课", module: "综合实践", name: "综合实践活动Ⅲ", code: "1109302703", exam: "考试", hours: 40, theory: 0, practice: 40, credits: 2, semester1: "", semester2: "", semester3: "40", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "公共必修课", module: "综合实践", name: "综合实践活动Ⅳ", code: "1109302704", exam: "考试", hours: 40, theory: 0, practice: 40, credits: 2, semester1: "", semester2: "", semester3: "", semester4: "40", semester5: "", semester6: "", note: ""},
            {category: "公共必修课", module: "综合实践", name: "综合实践活动Ⅴ", code: "1109302705", exam: "考试", hours: 40, theory: 0, practice: 40, credits: 2, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "40", semester6: "", note: ""},
            {category: "公共限选课", module: "外语素养", name: "基础应用英语 Ⅰ", code: "11093014", exam: "考试", hours: 30, theory: 24, practice: 6, credits: 1.5, semester1: "2*15W", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: "低要求"},
            {category: "公共限选课", module: "外语素养", name: "基础应用英语 Ⅱ", code: "11093015", exam: "考试", hours: 30, theory: 24, practice: 6, credits: 1.5, semester1: "", semester2: "2*15W", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "公共限选课", module: "自然科学", name: "高等数学（二）", code: "13033001", exam: "考试", hours: 56, theory: 56, practice: 0, credits: 3.5, semester1: "", semester2: "4*14W", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "公共限选课", module: "信息素养", name: "现代信息技术", code: "11093013", exam: "考试", hours: 48, theory: 16, practice: 32, credits: 3, semester1: "", semester2: "4*12W", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "公共限选课", module: "法律法规", name: "民法典与大学生活", code: "11093021", exam: "考试", hours: 16, theory: 16, practice: 0, credits: 1, semester1: "2*8W", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: "线上12学时"},
            {category: "公共限选课", module: "法律法规", name: "国家安全教育Ⅰ", code: "11093032", exam: "考试", hours: 8, theory: 4, practice: 4, credits: 0.5, semester1: "2*4W", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: "实践4学时"},
            {category: "公共限选课", module: "法律法规", name: "国家安全教育Ⅱ", code: "11093033", exam: "考试", hours: 8, theory: 4, practice: 4, credits: 0.5, semester1: "", semester2: "2*4W", semester3: "", semester4: "", semester5: "", semester6: "", note: "实践4学时"},
            {category: "公共限选课", module: "健康安全", name: "卫生健康教育", code: "11093020", exam: "考试", hours: 4, theory: 4, practice: 0, credits: 0.5, semester1: "2*2W", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: "线上4学时"},
            {category: "公共限选课", module: "健康安全", name: "大学生安全教育", code: "11093023", exam: "考试", hours: 32, theory: 32, practice: 0, credits: 2, semester1: "", semester2: "2*16W", semester3: "", semester4: "", semester5: "", semester6: "", note: "线上16学时"},
            {category: "公共限选课", module: "职业发展", name: "创新思维", code: "11093025", exam: "考试", hours: 32, theory: 24, practice: 8, credits: 2, semester1: "", semester2: "", semester3: "", semester4: "2*16W", semester5: "", semester6: "", note: ""},
            {category: "公共限选课", module: "职业发展", name: "大学生户外素质拓展训练", code: "11093022", exam: "考试", hours: 16, theory: 0, practice: 16, credits: 1, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "公共限选课", module: "职业发展", name: "大学生职业发展与就业指导1", code: "1109303501", exam: "考试", hours: 16, theory: 16, practice: 0, credits: 1, semester1: "", semester2: "2*8W", semester3: "", semester4: "", semester5: "", semester6: "", note: "大一学年"},
            {category: "公共限选课", module: "职业发展", name: "大学生职业发展与就业指导2", code: "1109303502", exam: "考试", hours: 16, theory: 16, practice: 0, credits: 1, semester1: "", semester2: "", semester3: "", semester4: "2*8W", semester5: "", semester6: "", note: "大二学年"},
            {category: "专业基础课", module: "专业基础课", name: "经济素养基础", code: "26053018", exam: "考查", hours: 48, theory: 32, practice: 16, credits: 3, semester1: "48", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业基础课", module: "专业基础课", name: "金融基础", code: "26013193", exam: "考查", hours: 48, theory: 32, practice: 16, credits: 3, semester1: "48", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业基础课", module: "专业基础课", name: "财务会计基础", code: "26013194", exam: "考试", hours: 48, theory: 24, practice: 24, credits: 3, semester1: "48", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业基础课", module: "专业基础课", name: "创业法律基础", code: "26023148", exam: "考查", hours: 48, theory: 24, practice: 24, credits: 3, semester1: "", semester2: "48", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业基础课", module: "专业基础课", name: "工商企业模拟(ERP)", code: "26053020", exam: "考查", hours: 40, theory: 0, practice: 40, credits: 2.5, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "40", semester6: "", note: ""},
            {category: "专业基础课", module: "专业基础课", name: "商科人工智能基础", code: "26013192", exam: "考查", hours: 40, theory: 20, practice: 20, credits: 2.5, semester1: "", semester2: "40", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业基础课", module: "专业基础课", name: "商业文书写作", code: "26043825", exam: "考查", hours: 32, theory: 16, practice: 16, credits: 2, semester1: "", semester2: "32", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业核心课", module: "专业核心课", name: "证券投资实务", code: "26043809", exam: "考查", hours: 56, theory: 32, practice: 24, credits: 3.5, semester1: "", semester2: "", semester3: "56", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业核心课", module: "专业核心课", name: "保险学基础（二）", code: "26043810", exam: "考试", hours: 40, theory: 24, practice: 16, credits: 2.5, semester1: "", semester2: "", semester3: "40", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业核心课", module: "专业核心课", name: "个人理财（二）", code: "26043811", exam: "考查", hours: 48, theory: 24, practice: 24, credits: 3, semester1: "", semester2: "", semester3: "", semester4: "48", semester5: "", semester6: "", note: ""},
            {category: "专业核心课", module: "专业核心课", name: "公司理财（二）", code: "26043812", exam: "考试", hours: 48, theory: 24, practice: 24, credits: 3, semester1: "", semester2: "", semester3: "", semester4: "48", semester5: "", semester6: "", note: ""},
            {category: "专业核心课", module: "专业核心课", name: "金融服务营销（二）", code: "26043813", exam: "考查", hours: 40, theory: 24, practice: 16, credits: 2.5, semester1: "", semester2: "", semester3: "", semester4: "40", semester5: "", semester6: "", note: ""},
            {category: "专业核心课", module: "专业核心课", name: "金融大数据处理", code: "26043815", exam: "考查", hours: 48, theory: 24, practice: 24, credits: 3, semester1: "", semester2: "48", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业核心课", module: "专业核心课", name: "数字信贷", code: "23053115", exam: "考查", hours: 40, theory: 24, practice: 16, credits: 2.5, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "40", semester6: "", note: ""},
            {category: "专业核心课", module: "专业核心课", name: "金融风险管理", code: "", exam: "考查", hours: 40, theory: 24, practice: 16, credits: 2.5, semester1: "", semester2: "", semester3: "", semester4: "40", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "财务会计基础实训", code: "26043103", exam: "考查", hours: 40, theory: 0, practice: 40, credits: 2, semester1: "2W", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "金融大数据处理实训", code: "23053116", exam: "考查", hours: 20, theory: 0, practice: 20, credits: 1, semester1: "", semester2: "1W", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "数字金融业务", code: "23053118", exam: "考查", hours: 48, theory: 0, practice: 48, credits: 3, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "银行从业资格考证", code: "", exam: "考查", hours: 40, theory: 20, practice: 20, credits: 2.5, semester1: "", semester2: "", semester3: "40", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "碳中和财会与金融基础", code: "23053119", exam: "考查", hours: 32, theory: 16, practice: 16, credits: 2, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "32", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "保险综合业务实训（二）", code: "26043768", exam: "考查", hours: 40, theory: 0, practice: 40, credits: 2, semester1: "", semester2: "", semester3: "2W", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "公司理财实训", code: "26043774", exam: "考查", hours: 40, theory: 0, practice: 40, credits: 2, semester1: "", semester2: "", semester3: "", semester4: "2W", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "证券综合模拟实训", code: "26043788", exam: "考查", hours: 20, theory: 0, practice: 20, credits: 1, semester1: "", semester2: "", semester3: "1W", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "个人理财实训", code: "26043795", exam: "考查", hours: 40, theory: 0, practice: 40, credits: 2, semester1: "", semester2: "", semester3: "", semester4: "2W", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "金融服务营销实训", code: "26043802", exam: "考查", hours: 20, theory: 0, practice: 40, credits: 1, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "供应链金融", code: "", exam: "考查", hours: 40, theory: 16, practice: 24, credits: 2.5, semester1: "", semester2: "", semester3: "", semester4: "40", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "金融科技概论（二）", code: "26043818", exam: "考查", hours: 48, theory: 16, practice: 32, credits: 3, semester1: "", semester2: "", semester3: "48", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "小微企业数字支付与运营", code: "", exam: "考查", hours: 40, theory: 0, practice: 40, credits: 2.5, semester1: "", semester2: "", semester3: "", semester4: "40", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "金融服务礼仪（二）", code: "26043820", exam: "考查", hours: 40, theory: 0, practice: 40, credits: 2.5, semester1: "", semester2: "", semester3: "", semester4: "40", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "商业银行综合柜台业务实训", code: "26043821", exam: "考查", hours: 40, theory: 0, practice: 40, credits: 2.5, semester1: "", semester2: "", semester3: "", semester4: "2W", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "证券投资基金（三）", code: "26043822", exam: "考查", hours: 48, theory: 12, practice: 36, credits: 3, semester1: "", semester2: "", semester3: "", semester4: "48", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "银行信贷实训", code: "26043823", exam: "考查", hours: 20, theory: 0, practice: 20, credits: 1, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "1W", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "EXCEL在财务管理中的应用（二）", code: "26043824", exam: "考查", hours: 48, theory: 0, practice: 48, credits: 3, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "48", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "区块链技术", code: "", exam: "考查", hours: 32, theory: 12, practice: 20, credits: 2, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "32", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "岗位实习", code: "26053010", exam: "考查", hours: 160, theory: 0, practice: 160, credits: 10, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "岗位实习II", code: "26053029", exam: "考查", hours: 320, theory: 0, practice: 320, credits: 16, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "数智化银行系统实践", code: "23053031", exam: "考查", hours: 128, theory: 0, practice: 128, credits: 8, semester1: "", semester2: "", semester3: "128", semester4: "", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "大数据精细化金融营销实践", code: "23053033", exam: "考查", hours: 128, theory: 0, practice: 128, credits: 8, semester1: "", semester2: "", semester3: "", semester4: "128", semester5: "", semester6: "", note: ""},
            {category: "专业拓展课", module: "专业拓展课", name: "金融科技行业认知", code: "23053034", exam: "考查", hours: 80, theory: 0, practice: 80, credits: 5, semester1: "", semester2: "", semester3: "", semester4: "", semester5: "80", semester6: "", note: ""}
        ];


        function assignCourseGroups() {
            let fintechCount = 0;
            let supplyChainCount = 0;
            
            coursesData.forEach(course => {

                if (!course.hasOwnProperty('courseGroup')) {
                    course.courseGroup = "";
                }
                
                if (courseGroups.fintech.includes(course.name)) {
                    course.courseGroup = "fintech";
                    fintechCount++;
                } else if (courseGroups["supply-chain"].includes(course.name)) {
                    course.courseGroup = "supply-chain";
                    supplyChainCount++;
                }
            });
            
            console.log(`课程群分配完成: 金融科技课程群 ${fintechCount} 门课程, 供应链金融课程群 ${supplyChainCount} 门课程`);
        }

        let filteredData = [];
        let selectedModule = "";
        let selectedCourseGroup = "";

        function initPage() {
            assignCourseGroups();
            filteredData = [...coursesData];
            renderTable(coursesData);
            updateStats(coursesData);
            initModuleButtons();
            initCourseGroupButtons();
            updateModuleStats();
        }


        function getCategoryClass(category) {
            const categoryMap = {
                '公共必修课': 'cat-public-required',
                '公共限选课': 'cat-public-elective',
                '专业基础课': 'cat-major-basic',
                '专业核心课': 'cat-major-core',
                '专业拓展课': 'cat-major-expand'
            };
            return categoryMap[category] || '';
        }

        // 获取模块CSS类名
        function getModuleClass(module) {
            const moduleMap = {
                '思想政治': 'mod-ideology',
                '体育健康': 'mod-sports',
                '军事教育': 'mod-military',
                '心理健康': 'mod-psychology',
                '劳动教育': 'mod-labor',
                '综合实践': 'mod-practice',
                '外语素养': 'mod-language',
                '自然科学': 'mod-science',
                '信息素养': 'mod-information',
                '法律法规': 'mod-law',
                '健康安全': 'mod-health',
                '职业发展': 'mod-career',
                '专业基础课': 'mod-major',
                '专业核心课': 'mod-major',
                '专业拓展课': 'mod-major'
            };
            return moduleMap[module] || '';
        }

        // 获取考核方式CSS类名
        function getExamClass(exam) {
            return exam === '考试' ? 'exam-test' : 'exam-check';
        }

        // 渲染表格
        function renderTable(data) {
            const tbody = document.getElementById('courseTableBody');
            if (!tbody) {
                console.error('表格body元素未找到');
                return;
            }
            tbody.innerHTML = '';
            
            data.forEach(course => {
                const row = document.createElement('tr');
                
                // 为课程群添加颜色标注
                if (course.courseGroup === "fintech") {
                    row.classList.add('fintech-course');
                } else if (course.courseGroup === "supply-chain") {
                    row.classList.add('supply-chain-course');
                }
                
                const categoryClass = getCategoryClass(course.category);
                const moduleClass = getModuleClass(course.module);
                const examClass = getExamClass(course.exam);
                
                row.innerHTML = `
                    <td><span class="table-btn btn-category ${categoryClass}">${course.category}</span></td>
                    <td><span class="table-btn btn-module ${moduleClass}">${course.module}</span></td>
                    <td><span class="table-btn btn-course-name">${course.name}</span></td>
                    <td>${course.code}</td>
                    <td><span class="table-btn btn-exam ${examClass}">${course.exam}</span></td>
                    <td>${course.hours}</td>
                    <td>${course.theory}</td>
                    <td>${course.practice}</td>
                    <td>${course.credits}</td>
                    <td>${course.semester1}</td>
                    <td>${course.semester2}</td>
                    <td>${course.semester3}</td>
                    <td>${course.semester4}</td>
                    <td>${course.semester5}</td>
                    <td>${course.semester6}</td>
                    <td>${course.note}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // 更新统计信息
        function updateStats(data) {
            const totalCourses = data.length;
            const totalCredits = data.reduce((sum, course) => {
                const credits = parseFloat(course.credits) || 0;
                return sum + credits;
            }, 0);
            const totalHours = data.reduce((sum, course) => {
                const hours = parseInt(course.hours) || 0;
                return sum + hours;
            }, 0);

            const totalCoursesEl = document.getElementById('totalCourses');
            const totalCreditsEl = document.getElementById('totalCredits');
            const totalHoursEl = document.getElementById('totalHours');
            const filteredCoursesEl = document.getElementById('filteredCourses');
            
            if (totalCoursesEl) totalCoursesEl.textContent = totalCourses;
            if (totalCreditsEl) totalCreditsEl.textContent = totalCredits.toFixed(1);
            if (totalHoursEl) totalHoursEl.textContent = totalHours;
            if (filteredCoursesEl) filteredCoursesEl.textContent = filteredData.length;
            
            // 更新表格头部的结果计数和筛选状态
            let statusText = `共找到 ${filteredData.length} 门课程`;
            if (selectedCourseGroup) {
                const groupName = selectedCourseGroup === 'fintech' ? '金融科技课程群' : '供应链金融课程群';
                statusText += ` (${groupName})`;
            }
            if (selectedModule) {
                statusText += ` (${selectedModule})`;
            }
            
            const resultCountEl = document.getElementById('resultCount');
            const resultInfoEl = document.querySelector('.result-info');
            
            if (resultCountEl) resultCountEl.innerHTML = filteredData.length;
            if (resultInfoEl) resultInfoEl.innerHTML = statusText;
        }

        // 初始化模块按钮
        function initModuleButtons() {
            const moduleButtons = document.querySelectorAll('.module-btn');
            console.log(`找到 ${moduleButtons.length} 个模块按钮`);
            
            moduleButtons.forEach((btn, index) => {
                console.log(`初始化第 ${index + 1} 个模块按钮: ${btn.textContent}`);
                btn.addEventListener('click', function() {
                    console.log(`模块按钮被点击: ${this.textContent}`);
                    
                    // 移除所有active类
                    moduleButtons.forEach(b => b.classList.remove('active'));
                    // 添加active类到当前按钮
                    this.classList.add('active');
                    // 更新选中的模块
                    selectedModule = this.getAttribute('data-module');
                    console.log(`选中的模块: "${selectedModule}"`);
                    
                    // 课程群筛选始终显示，无需特殊处理
                    
                    // 更新模块统计
                    updateModuleStats();
                    // 应用筛选
                    applyFilters();
                    
                    // 选择模块后跳转到表格
                    console.log('即将跳转到表格区域...');
                    setTimeout(() => {
                        performVisibleJump();
                    }, 300);
                });
            });
        }

        // 初始化课程群按钮
        function initCourseGroupButtons() {
            const courseGroupButtons = document.querySelectorAll('.course-group-btn');
            courseGroupButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有active类
                    courseGroupButtons.forEach(b => b.classList.remove('active'));
                    // 添加active类到当前按钮
                    this.classList.add('active');
                    // 更新选中的课程群
                    selectedCourseGroup = this.getAttribute('data-group');
                    
                    // 显示/隐藏对应的课程群介绍
                    showCourseGroupInfo(selectedCourseGroup);
                    
                    // 应用筛选
                    applyFilters();
                    
                    // 选择课程群后跳转到表格并显示结果
                    setTimeout(() => {
                        performVisibleJump();
                        // 显示筛选提示
                        if (selectedCourseGroup) {
                            const groupName = selectedCourseGroup === 'fintech' ? '金融科技课程群' : '供应链金融课程群';
                            showNotification(`已筛选显示${groupName}相关课程`, 'success');
                        } else {
                            showNotification('已显示全部课程', 'info');
                        }
                    }, 300);
                });
            });
        }

        // 显示课程群介绍
        function showCourseGroupInfo(groupType) {
            const courseGroupsSection = document.getElementById('courseGroupsSection');
            const fintechGroup = document.getElementById('fintechGroup');
            const supplyChainGroup = document.getElementById('supplyChainGroup');
            
            // 先隐藏所有课程群
            fintechGroup.style.display = 'none';
            supplyChainGroup.style.display = 'none';
            
            if (groupType === '') {
                // 如果选择"显示全部"，隐藏整个课程群部分
                courseGroupsSection.style.display = 'none';
            } else {
                // 显示课程群部分
                courseGroupsSection.style.display = 'block';
                
                // 根据选择显示对应的课程群
                if (groupType === 'fintech') {
                    fintechGroup.style.display = 'block';
                } else if (groupType === 'supply-chain') {
                    supplyChainGroup.style.display = 'block';
                }
                
                // 平滑滚动到课程群介绍部分
                setTimeout(() => {
                    courseGroupsSection.scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'start' 
                    });
                }, 100);
            }
        }

        // 更新模块统计
        function updateModuleStats() {
            let moduleData = coursesData;
            
            // 如果选择了特定模块，只统计该模块的数据
            if (selectedModule) {
                moduleData = coursesData.filter(course => course.module === selectedModule);
            }
            
            const totalHours = moduleData.reduce((sum, course) => {
                const hours = parseInt(course.hours) || 0;
                return sum + hours;
            }, 0);
            
            const totalCredits = moduleData.reduce((sum, course) => {
                const credits = parseFloat(course.credits) || 0;
                return sum + credits;
            }, 0);
            
            const moduleHoursEl = document.getElementById('moduleHours');
            const moduleCreditsEl = document.getElementById('moduleCredits');
            
            if (moduleHoursEl) moduleHoursEl.textContent = totalHours;
            if (moduleCreditsEl) moduleCreditsEl.textContent = totalCredits.toFixed(1);
        }

        // 应用筛选
        function applyFilters() {
            const categoryFilterEl = document.getElementById('categoryFilter');
            const semesterFilterEl = document.getElementById('semesterFilter');
            const typeFilterEl = document.getElementById('typeFilter');
            const searchInputEl = document.getElementById('searchInput');
            
            if (!categoryFilterEl || !semesterFilterEl || !typeFilterEl || !searchInputEl) {
                console.error('筛选元素未找到');
                return;
            }
            
            const categoryFilter = categoryFilterEl.value;
            const semesterFilter = semesterFilterEl.value;
            const typeFilter = typeFilterEl.value;
            const searchInput = searchInputEl.value.toLowerCase();

            filteredData = coursesData.filter(course => {
                // 课程类别筛选
                if (categoryFilter && course.category !== categoryFilter) return false;
                
                // 模块筛选（使用按钮选择的模块）
                if (selectedModule && course.module !== selectedModule) return false;
                
                // 课程群筛选（始终生效）
                if (selectedCourseGroup && course.courseGroup !== selectedCourseGroup) return false;
                
                // 学期筛选
                if (semesterFilter) {
                    const semesterKey = `semester${semesterFilter}`;
                    if (!course[semesterKey] || course[semesterKey].trim() === '') return false;
                }
                
                // 课程性质筛选
                if (typeFilter) {
                    const theory = parseInt(course.theory) || 0;
                    const practice = parseInt(course.practice) || 0;
                    const total = theory + practice;
                    
                    if (total === 0) return typeFilter !== 'practice';
                    
                    const theoryRatio = theory / total;
                    const practiceRatio = practice / total;
                    
                    if (typeFilter === 'theory' && theoryRatio <= 0.6) return false;
                    if (typeFilter === 'practice' && practiceRatio <= 0.6) return false;
                    if (typeFilter === 'mixed' && (theoryRatio < 0.3 || practiceRatio < 0.3)) return false;
                }
                
                // 课程名称搜索
                if (searchInput && !course.name.toLowerCase().includes(searchInput)) return false;
                
                return true;
            });

            renderTable(filteredData);
            updateStats(coursesData);
            
            // 调试信息：输出当前筛选条件和结果
            console.log(`筛选条件: 课程群="${selectedCourseGroup}", 模块="${selectedModule}", 结果: ${filteredData.length} 门课程`);
        }

        // 重置筛选
        function resetFilters() {
            document.getElementById('categoryFilter').value = '';
            document.getElementById('semesterFilter').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('searchInput').value = '';
            
            // 重置模块按钮
            const moduleButtons = document.querySelectorAll('.module-btn');
            moduleButtons.forEach(btn => btn.classList.remove('active'));
            document.querySelector('.module-btn[data-module=""]').classList.add('active');
            selectedModule = "";
            
            // 重置课程群按钮
            const courseGroupButtons = document.querySelectorAll('.course-group-btn');
            courseGroupButtons.forEach(btn => btn.classList.remove('active'));
            document.querySelector('.course-group-btn[data-group=""]').classList.add('active');
            selectedCourseGroup = "";
            
            // 隐藏课程群介绍部分
            document.getElementById('courseGroupsSection').style.display = 'none';
            
            // 更新模块统计
            updateModuleStats();
            
            filteredData = [...coursesData];
            renderTable(filteredData);
            updateStats(coursesData);
            
            // 显示重置成功提示
            showNotification('筛选条件已重置', 'info');
        }

        // 筛选查询并跳转到表格
        function searchAndJump() {
            console.log('筛选查询按钮被点击');
            
            // 通过选择器获取按钮，而不是依赖event.target
            const searchBtn = document.querySelector('button[onclick="searchAndJump()"]');
            
            if (searchBtn) {
                // 添加加载状态
                searchBtn.classList.add('btn-loading');
                searchBtn.disabled = true;
            }
            
            // 执行筛选（缩短延迟时间）
            setTimeout(() => {
                try {
                    console.log('开始执行筛选');
                    applyFilters();
                    
                    if (searchBtn) {
                        // 移除加载状态
                        searchBtn.classList.remove('btn-loading');
                        searchBtn.disabled = false;
                        console.log('加载状态已移除');
                    }
                    
                    // 强制跳转到表格区域，使用更明显的效果
                    performVisibleJump();
                    
                    // 显示成功提示
                    showNotification(`查询完成！找到 ${filteredData.length} 门课程`, 'success');
                    
                } catch (error) {
                    console.error('筛选过程出错:', error);
                    if (searchBtn) {
                        // 确保移除加载状态
                        searchBtn.classList.remove('btn-loading');
                        searchBtn.disabled = false;
                    }
                    showNotification('查询过程出现错误', 'error');
                }
            }, 300);
        }
        
        // 执行明显的跳转效果
        function performVisibleJump() {
            console.log('执行明显的跳转效果');
            const tableContainer = document.getElementById('tableContainer');
            
            if (!tableContainer) {
                console.error('未找到表格容器');
                return;
            }
            
            // 先回到页面顶部，然后再跳转到表格
            window.scrollTo({
                top: 0,
                behavior: 'instant'
            });
            
            // 短暂延迟后跳转到表格
            setTimeout(() => {
                const rect = tableContainer.getBoundingClientRect();
                const offset = window.pageYOffset + rect.top - 80;
                
                window.scrollTo({
                    top: offset,
                    behavior: 'smooth'
                });
                
                // 添加明显的高亮效果
                tableContainer.style.transition = 'all 0.5s ease';
                tableContainer.style.transform = 'scale(1.02)';
                tableContainer.style.boxShadow = '0 12px 40px rgba(79, 70, 229, 0.4)';
                tableContainer.style.borderRadius = '12px';
                
                // 2秒后恢复正常
                setTimeout(() => {
                    tableContainer.style.transform = 'scale(1)';
                    tableContainer.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
                }, 2000);
                
            }, 100);
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateY(0)';
            }, 100);
            
            // 3秒后自动移除
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 导出数据
        function exportData() {
            let csvContent = "课程类别,模块,课程名称,课程代码,考核方式,学时数,理论,实践,学分,第1学期,第2学期,第3学期,第4学期,第5学期,第6学期,备注\n";
            
            filteredData.forEach(course => {
                const row = [
                    course.category, course.module, course.name, course.code, course.exam,
                    course.hours, course.theory, course.practice, course.credits,
                    course.semester1, course.semester2, course.semester3, 
                    course.semester4, course.semester5, course.semester6, course.note
                ].map(field => `"${field}"`).join(',');
                csvContent += row + "\n";
            });

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement("a");
            const url = URL.createObjectURL(blob);
            link.setAttribute("href", url);
            link.setAttribute("download", "课程体系数据.csv");
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 切换课程描述显示
        function toggleCourse(element) {
            const description = element.nextElementSibling;
            const isHidden = description.classList.contains('hidden');
            
            if (isHidden) {
                description.classList.remove('hidden');
                element.classList.remove('collapsed');
            } else {
                description.classList.add('hidden');
                element.classList.add('collapsed');
            }
        }

        // 跳转到表格区域（简化版，使用统一的明显跳转效果）
        function jumpToTable() {
            console.log('jumpToTable 函数被调用，使用明显跳转效果');
            performVisibleJump();
        }

        // 回到顶部
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 监听滚动，控制回到顶部按钮显示
        function handleScroll() {
            const backToTopBtn = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面 DOM 加载完成，开始初始化...');
            
            // 检查必要的元素是否存在
            const tableContainer = document.getElementById('tableContainer');
            const moduleButtons = document.querySelectorAll('.module-btn');
            
            console.log('表格容器:', tableContainer ? '找到' : '未找到');
            console.log('模块按钮数量:', moduleButtons.length);
            
            if (!tableContainer) {
                console.error('严重错误：未找到表格容器元素');
                return;
            }
            
            if (moduleButtons.length === 0) {
                console.error('严重错误：未找到任何模块按钮');
                return;
            }
            
            // 初始化页面
            initPage();
            
            // 添加滚动监听
            window.addEventListener('scroll', handleScroll);
            
            console.log('页面初始化完成');
            
            // 添加一个测试函数到全局作用域，方便调试
            window.testJump = function() {
                console.log('测试跳转功能...');
                performVisibleJump();
            };
            
            console.log('可以在浏览器控制台输入 testJump() 来测试跳转功能');
        });


        let mindmapData = null;
        let zoomLevel = 1;
        let panX = 0;
        let panY = 0;
        let isDragging = false;
        let dragStartX = 0;
        let dragStartY = 0;


        function generateMindMap() {
            console.log('开始生成思维导图...');
            
            // 构建思维导图数据结构
            mindmapData = buildMindMapData();
            
            // 显示弹窗
            document.getElementById('mindmapModal').style.display = 'flex';
            
            // 渲染思维导图
            setTimeout(() => {
                renderMindMap();
                initMindMapInteraction();
                showNotification('🚀 课程体系思维导图已生成！中心橙色圆形，类别绿色圆角矩形，课程均匀分布，字体清晰可见', 'success');
            }, 100);
        }

        // 构建思维导图数据结构 - 按课程类别左右分布
        function buildMindMapData() {
            const data = {
                id: 'center',
                name: '2025级金融服务与管理专业课程体系',
                type: 'center',
                x: 0,
                y: 0,
                expanded: true,
                children: []
            };

            // 按课程类别分组
            const categoryGroups = {};
            coursesData.forEach(course => {
                if (!categoryGroups[course.category]) {
                    categoryGroups[course.category] = [];
                }
                categoryGroups[course.category].push(course);
            });

            // 构建课程类别节点（5个类别直接跟课程）
            Object.keys(categoryGroups).forEach((category, categoryIndex) => {
                const categoryNode = {
                    id: `category-${categoryIndex}`,
                    name: category,
                    type: 'category',
                    expanded: true,
                    children: []
                };

                // 构建课程节点
                categoryGroups[category].forEach((course, courseIndex) => {
                    const courseNode = {
                        id: `course-${categoryIndex}-${courseIndex}`,
                        name: course.name,
                        type: 'course',
                        category: category,
                        data: course
                    };
                    categoryNode.children.push(courseNode);
                });

                data.children.push(categoryNode);
            });

            return data;
        }

        // 渲染思维导图
        function renderMindMap() {
            const svg = document.getElementById('mindmapSvg');
            const group = document.getElementById('mindmapGroup');
            
            // 清空之前的内容
            group.innerHTML = '';
            
            // 使用实际屏幕尺寸而不是固定尺寸
            const containerWidth = window.innerWidth;
            const containerHeight = window.innerHeight;
            const centerX = containerWidth / 2;
            const centerY = containerHeight / 2;
            
            // 设置中心节点位置
            mindmapData.x = centerX;
            mindmapData.y = centerY;
            
            // 计算布局
            calculateLayout(mindmapData, centerX, centerY);
            
            // 渲染节点和连线
            renderNodes(group, mindmapData);
            
            // 计算并设置最佳缩放级别
            const optimalZoom = calculateOptimalZoom(group, containerWidth, containerHeight);
            zoomLevel = optimalZoom;
            panX = 0;
            panY = 0;
            
            // 应用变换
            applyTransform();
        }

        // 5区域固定布局系统 - 优化为全屏尺寸
        function calculateLayout(node, centerX, centerY) {
            if (node.type === 'center') {
                // 使用实际屏幕尺寸而不是固定尺寸
                const containerWidth = window.innerWidth;
                const containerHeight = window.innerHeight;
                
                // 重新计算中心位置
                centerX = containerWidth / 2;
                centerY = containerHeight / 2;
                
                // 中心节点位置
                node.x = centerX;
                node.y = centerY;
                
                // 全新分散式布局设计，充分利用页面边缘，确保无重叠
                const margin = 40;
                const centerSafeZone = 180; // 中心安全区域半径
                
                // 重新优化布局：分散式设计，确保5个区域完全独立，杜绝重叠
                // 大区域：专业拓展课(24门)、公共必修课(23门)、公共限选课(14门) - 占据大片区域
                // 小区域：专业核心课(8门)、专业基础课(7门) - 占据独立的小区域
                const regions = [
                    {
                        name: '区域1-左上（公共必修课-大区域）',
                        categoryX: containerWidth * 0.2,
                        categoryY: containerHeight * 0.25,
                        bounds: {
                            left: margin,
                            right: centerX - centerSafeZone,
                            top: margin,
                            bottom: centerY - 40
                        }
                    },
                    {
                        name: '区域2-右上（专业拓展课-大区域）',
                        categoryX: containerWidth * 0.8,
                        categoryY: containerHeight * 0.25,
                        bounds: {
                            left: centerX + centerSafeZone,
                            right: containerWidth - margin,
                            top: margin,
                            bottom: centerY - 40
                        }
                    },
                    {
                        name: '区域3-左下（公共限选课-大区域）',
                        categoryX: containerWidth * 0.2,
                        categoryY: containerHeight * 0.75,
                        bounds: {
                            left: margin,
                            right: centerX - centerSafeZone,
                            top: centerY + 40,
                            bottom: containerHeight - margin
                        }
                    },
                    {
                        name: '区域4-右中（专业核心课-独立小区域）',
                        categoryX: containerWidth * 0.75,
                        categoryY: centerY + containerHeight * 0.12,
                        bounds: {
                            left: centerX + centerSafeZone,
                            right: containerWidth - margin,
                            top: centerY + 40,
                            bottom: centerY + (containerHeight - centerY) * 0.45
                        }
                    },
                    {
                        name: '区域5-右下（专业基础课-独立小区域）',
                        categoryX: containerWidth * 0.75,
                        categoryY: centerY + (containerHeight - centerY) * 0.8,
                        bounds: {
                            left: centerX + centerSafeZone,
                            right: containerWidth - margin,
                            top: centerY + (containerHeight - centerY) * 0.55,
                            bottom: containerHeight - margin
                        }
                    }
                ];
                
                // 精确指定每个课程类别到区域的映射
                const categoryToRegionMap = {
                    '公共必修课': 0,      // 区域1-上半左侧（大区域）
                    '专业拓展课': 1,      // 区域2-上半右侧（大区域）
                    '公共限选课': 2,      // 区域3-下半左侧（大区域）
                    '专业核心课': 3,      // 区域4-下半右上（小区域）
                    '专业基础课': 4       // 区域5-下半右下（小区域）
                };
                
                // 为每个类别分配独立区域
                node.children.forEach((categoryNode, categoryIndex) => {
                    const categoryName = categoryNode.name;
                    const regionIndex = categoryToRegionMap[categoryName];
                    
                    if (regionIndex !== undefined && regionIndex < regions.length) {
                        const region = regions[regionIndex];
                        
                        // 设置类别节点位置
                        categoryNode.x = region.categoryX;
                        categoryNode.y = region.categoryY;
                        
                        // 在分配的区域内分布课程
                        if (categoryNode.children && categoryNode.children.length > 0) {
                            distributeCourseInFixedRegion(categoryNode, region);
                        }
                    }
                });
            }
        }
        
        // 在固定区域内智能分布课程 - 完全避免重叠
        function distributeCourseInFixedRegion(categoryNode, region) {
            const courseCount = categoryNode.children.length;
            if (courseCount === 0) return;
            
            const categoryX = categoryNode.x;
            const categoryY = categoryNode.y;
            const bounds = region.bounds;
            
            // 计算可用区域尺寸
            const regionWidth = bounds.right - bounds.left;
            const regionHeight = bounds.bottom - bounds.top;
            
            // 课程按钮尺寸和间距（优化设计）
            const courseWidth = 120;  // 减小按钮宽度，增加容纳空间
            const courseHeight = 35;  // 减小按钮高度
            const minSpacing = 25;    // 增加最小间距，确保清晰分离
            const categoryBuffer = 100; // 增加类别节点周围的缓冲区
            
            // 计算可容纳的网格
            const gridCellWidth = courseWidth + minSpacing;
            const gridCellHeight = courseHeight + minSpacing;
            const maxCols = Math.floor(regionWidth / gridCellWidth);
            const maxRows = Math.floor(regionHeight / gridCellHeight);
            
            // 智能网格布局算法 - 根据课程数量和区域大小优化
            let actualCols, actualRows;
            
            if (courseCount <= 8) {
                // 小区域（专业核心课、专业基础课）：紧凑布局
                actualCols = Math.min(maxCols, Math.ceil(Math.sqrt(courseCount * 1.5)));
                actualCols = Math.max(2, actualCols); // 最少2列
            } else if (courseCount <= 15) {
                // 中等区域（公共限选课）：适中布局
                actualCols = Math.min(maxCols, Math.ceil(courseCount / 3));
            } else {
                // 大区域（公共必修课、专业拓展课）：多列布局
                actualCols = Math.min(maxCols, Math.ceil(courseCount / 4));
                actualCols = Math.max(4, actualCols); // 最少4列
            }
            
            actualRows = Math.ceil(courseCount / actualCols);
            
            // 确保能容纳所有课程
            while (actualCols * actualRows < courseCount) {
                if (actualCols < maxCols) {
                    actualCols++;
                } else {
                    actualRows++;
                }
            }
            
            // 优化间距分配
            let actualSpacingX = Math.max(minSpacing, (regionWidth - actualCols * courseWidth) / (actualCols + 1));
            let actualSpacingY = Math.max(minSpacing, (regionHeight - actualRows * courseHeight) / (actualRows + 1));
            
            // 优化网格布局，采用多层次避免重叠策略
            categoryNode.children.forEach((course, index) => {
                // 计算初始网格位置
                const row = Math.floor(index / actualCols);
                const col = index % actualCols;
                
                let x = bounds.left + actualSpacingX + col * (courseWidth + actualSpacingX) + courseWidth / 2;
                let y = bounds.top + actualSpacingY + row * (courseHeight + actualSpacingY) + courseHeight / 2;
                
                // 第一层：避免与类别节点重合
                const dxToCategory = x - categoryX;
                const dyToCategory = y - categoryY;
                const distanceToCategory = Math.sqrt(dxToCategory * dxToCategory + dyToCategory * dyToCategory);
                
                if (distanceToCategory < categoryBuffer) {
                    // 计算调整方向（远离类别节点）
                    const adjustAngle = Math.atan2(dyToCategory, dxToCategory);
                    const adjustDistance = categoryBuffer - distanceToCategory + 30; // 增加额外缓冲
                    
                    x += Math.cos(adjustAngle) * adjustDistance;
                    y += Math.sin(adjustAngle) * adjustDistance;
                }
                
                // 第二层：智能边界约束
                x = Math.max(bounds.left + courseWidth/2 + 15, 
                     Math.min(x, bounds.right - courseWidth/2 - 15));
                y = Math.max(bounds.top + courseHeight/2 + 15, 
                     Math.min(y, bounds.bottom - courseHeight/2 - 15));
                
                // 第三层：高精度重叠检测和解决
                let adjustAttempts = 0;
                const maxAdjustAttempts = 25; // 增加调整次数
                const safeDistance = courseWidth + minSpacing + 15; // 更大的安全距离
                
                while (adjustAttempts < maxAdjustAttempts) {
                    let hasOverlap = false;
                    let closestDistance = Infinity;
                    let bestAdjustAngle = 0;
                    
                    // 检查与所有已放置课程的距离
                    for (let i = 0; i < index; i++) {
                        const otherCourse = categoryNode.children[i];
                        if (otherCourse.x !== undefined && otherCourse.y !== undefined) {
                            const dx = x - otherCourse.x;
                            const dy = y - otherCourse.y;
                            const distance = Math.sqrt(dx * dx + dy * dy);
                            
                            if (distance < safeDistance) {
                                hasOverlap = true;
                                if (distance < closestDistance) {
                                    closestDistance = distance;
                                    bestAdjustAngle = Math.atan2(dy, dx);
                                }
                            }
                        }
                    }
                    
                    if (!hasOverlap) {
                        break;
                    }
                    
                    // 智能调整：向最佳方向推移
                    const pushDistance = safeDistance - closestDistance + 20;
                    x += Math.cos(bestAdjustAngle) * pushDistance;
                    y += Math.sin(bestAdjustAngle) * pushDistance;
                    
                    // 重新应用边界约束
                    x = Math.max(bounds.left + courseWidth/2 + 15, 
                         Math.min(x, bounds.right - courseWidth/2 - 15));
                    y = Math.max(bounds.top + courseHeight/2 + 15, 
                         Math.min(y, bounds.bottom - courseHeight/2 - 15));
                    
                    adjustAttempts++;
                }
                
                // 最终验证：如果仍有重叠，采用螺旋搜索策略
                if (adjustAttempts >= maxAdjustAttempts) {
                    let spiralRadius = 30;
                    let spiralAngle = 0;
                    let found = false;
                    
                    for (let spiral = 0; spiral < 12 && !found; spiral++) {
                        for (let angleStep = 0; angleStep < 8 && !found; angleStep++) {
                            const testX = x + Math.cos(spiralAngle) * spiralRadius;
                            const testY = y + Math.sin(spiralAngle) * spiralRadius;
                            
                            // 检查边界
                            if (testX >= bounds.left + courseWidth/2 + 15 && 
                                testX <= bounds.right - courseWidth/2 - 15 &&
                                testY >= bounds.top + courseHeight/2 + 15 && 
                                testY <= bounds.bottom - courseHeight/2 - 15) {
                                
                                // 检查重叠
                                let noOverlap = true;
                                for (let i = 0; i < index; i++) {
                                    const otherCourse = categoryNode.children[i];
                                    if (otherCourse.x !== undefined && otherCourse.y !== undefined) {
                                        const dx = testX - otherCourse.x;
                                        const dy = testY - otherCourse.y;
                                        const distance = Math.sqrt(dx * dx + dy * dy);
                                        
                                        if (distance < safeDistance) {
                                            noOverlap = false;
                                            break;
                                        }
                                    }
                                }
                                
                                if (noOverlap) {
                                    x = testX;
                                    y = testY;
                                    found = true;
                                }
                            }
                            
                            spiralAngle += Math.PI / 4; // 45度步进
                        }
                        spiralRadius += 20;
                    }
                }
                
                course.x = x;
                course.y = y;
            });
        }



        // 计算最佳缩放级别
        function calculateOptimalZoom(group, containerWidth, containerHeight) {
            // 获取所有节点的边界
            let minX = Infinity, maxX = -Infinity;
            let minY = Infinity, maxY = -Infinity;
            
            function getBounds(node) {
                if (node.x !== undefined && node.y !== undefined) {
                    // 为不同类型节点设置不同的边距
                    let margin = 40;
                    if (node.type === 'center') margin = 80;
                    else if (node.type === 'category') margin = 60;
                    else if (node.type === 'module') margin = 50;
                    else if (node.type === 'course') margin = 40;
                    
                    minX = Math.min(minX, node.x - margin);
                    maxX = Math.max(maxX, node.x + margin);
                    minY = Math.min(minY, node.y - margin);
                    maxY = Math.max(maxY, node.y + margin);
                }
                
                if (node.children && node.expanded) {
                    node.children.forEach(child => getBounds(child));
                }
            }
            
            getBounds(mindmapData);
            
            // 计算内容的实际尺寸
            const contentWidth = maxX - minX;
            const contentHeight = maxY - minY;
            
            // 计算缩放比例，保留更多边距
            const scaleX = (containerWidth * 0.85) / contentWidth;
            const scaleY = (containerHeight * 0.85) / contentHeight;
            
            // 选择较小的缩放比例，确保内容完全可见
            const optimalScale = Math.min(scaleX, scaleY, 0.9); // 最大缩放0.9
            
            return Math.max(optimalScale, 0.2); // 最小缩放0.2
        }

        // 文本换行函数
        function wrapText(text, maxLength) {
            if (text.length <= maxLength) {
                return [text];
            }
            
            const words = text.split('');
            const lines = [];
            let currentLine = '';
            
            for (let char of words) {
                if (currentLine.length + 1 <= maxLength) {
                    currentLine += char;
                } else {
                    lines.push(currentLine);
                    currentLine = char;
                }
            }
            
            if (currentLine) {
                lines.push(currentLine);
            }
            
            return lines;
        }

        // 计算连接点到节点边缘
        function getConnectionPoint(fromNode, toNode) {
            const dx = toNode.x - fromNode.x;
            const dy = toNode.y - fromNode.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance === 0) return { x: fromNode.x, y: fromNode.y };
            
            // 计算节点尺寸
            const getNodeSize = (node) => {
                switch(node.type) {
                    case 'center':
                        return { width: 200, height: 60 };
                    case 'category':
                        return { width: 120, height: 50 };
                    case 'course':
                        return { width: 120, height: 35 };
                    default:
                        return { width: 100, height: 40 };
                }
            };
            
            const fromSize = getNodeSize(fromNode);
            const toSize = getNodeSize(toNode);
            
            // 计算边缘交点
            const fromRadius = Math.max(fromSize.width/2, fromSize.height/2);
            const toRadius = Math.max(toSize.width/2, toSize.height/2);
            
            const fromX = fromNode.x + (dx / distance) * fromRadius;
            const fromY = fromNode.y + (dy / distance) * fromRadius;
            const toX = toNode.x - (dx / distance) * toRadius;
            const toY = toNode.y - (dy / distance) * toRadius;
            
            return { from: { x: fromX, y: fromY }, to: { x: toX, y: toY } };
        }

        // 渲染节点
        function renderNodes(container, node) {
            // 渲染连线
            if (node.children && node.expanded) {
                node.children.forEach(child => {
                    if (child.x !== undefined && child.y !== undefined) {
                        // 创建曲线连接而不是直线
                        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                        path.setAttribute('class', 'mindmap-line');
                        
                        // 计算连接点到节点边缘
                        const connectionPoints = getConnectionPoint(node, child);
                        const startX = connectionPoints.from.x;
                        const startY = connectionPoints.from.y;
                        const endX = connectionPoints.to.x;
                        const endY = connectionPoints.to.y;
                        
                        // 计算控制点，创建优美的曲线
                        const dx = endX - startX;
                        const dy = endY - startY;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        const controlOffset = distance * 0.3;
                        
                        // 控制点位于连线中点的垂直方向
                        const midX = (startX + endX) / 2;
                        const midY = (startY + endY) / 2;
                        const angle = Math.atan2(dy, dx) + Math.PI / 2;
                        const controlX = midX + Math.cos(angle) * controlOffset * 0.2;
                        const controlY = midY + Math.sin(angle) * controlOffset * 0.2;
                        
                        const pathData = `M ${startX} ${startY} Q ${controlX} ${controlY} ${endX} ${endY}`;
                        path.setAttribute('d', pathData);
                        
                        // 根据层级设置不同的线条样式和流动效果
                        if (child.type === 'category') {
                            path.setAttribute('stroke-width', '3');
                            path.setAttribute('stroke', '#10b981');
                            path.setAttribute('stroke-opacity', '0.8');
                            
                            // 添加流动光点效果
                            const flowingPath = path.cloneNode();
                            flowingPath.setAttribute('stroke', 'url(#flowingDots)');
                            flowingPath.setAttribute('stroke-width', '2');
                            flowingPath.classList.add('flowing-line');
                            container.appendChild(flowingPath);
                        } else if (child.type === 'course') {
                            path.setAttribute('stroke-width', '2');
                            path.setAttribute('stroke', '#059669');
                            path.setAttribute('stroke-opacity', '0.6');
                        } else {
                            path.setAttribute('stroke-width', '1.5');
                            path.setAttribute('stroke', 'var(--neutral-gray)');
                            path.setAttribute('stroke-opacity', '0.4');
                        }
                        
                        container.appendChild(path);
                        
                        // 递归渲染子节点
                        renderNodes(container, child);
                    }
                });
            }
            
            // 渲染节点
            const nodeGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            const categoryClass = node.type === 'course' ? getCategoryClass(node.category) : getCategoryClass(node.name);
            nodeGroup.setAttribute('class', `mindmap-node ${node.type}-node ${categoryClass}`);
            nodeGroup.setAttribute('data-id', node.id);
            
            // 计算节点尺寸（根据文本行数动态调整）
            const maxLineLength = node.type === 'center' ? 12 : node.type === 'category' ? 8 : node.type === 'course' ? 12 : 10;
            const lines = wrapText(node.name, maxLineLength);
            const lineHeight = node.type === 'center' ? 18 : node.type === 'category' ? 14 : node.type === 'course' ? 12 : 11;
            
            let width, height;
            
            switch(node.type) {
                case 'center':
                    width = Math.max(maxLineLength * 12 + 40, 200);
                    height = Math.max(lines.length * lineHeight + 30, 60);
                    break;
                case 'category':
                    width = Math.max(maxLineLength * 9 + 20, 120);
                    height = Math.max(lines.length * lineHeight + 20, 50);
                    break;
                case 'course':
                    width = Math.max(maxLineLength * 6 + 15, 120);
                    height = Math.max(lines.length * lineHeight + 15, 35);
                    break;
                default:
                    width = Math.max(maxLineLength * 6 + 15, 70);
                    height = Math.max(lines.length * lineHeight + 15, 25);
            }
            
            // 创建节点形状
            let shape;
            
            if (node.type === 'center') {
                // 中心节点 - 无背景，只有文字
                shape = null;
            } else if (node.type === 'category') {
                // 类别节点 - 圆角矩形边框
                shape = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                shape.setAttribute('x', node.x - 60);
                shape.setAttribute('y', node.y - 25);
                shape.setAttribute('width', 120);
                shape.setAttribute('height', 50);
                shape.setAttribute('rx', 15);
                shape.setAttribute('ry', 15);
                shape.setAttribute('class', 'node-shape category-node');
            } else if (node.type === 'course') {
                // 课程节点 - 圆角矩形背景
                shape = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
                shape.setAttribute('x', node.x - 60);
                shape.setAttribute('y', node.y - 17.5);
                shape.setAttribute('width', 120);
                shape.setAttribute('height', 35);
                shape.setAttribute('rx', 17.5);
                shape.setAttribute('ry', 17.5);
                shape.setAttribute('class', 'node-shape course-button');
            }
            
            if (shape) {
                nodeGroup.appendChild(shape);
            }
            
            // 节点文本（支持自动换行）
            const textGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            const startY = node.y - (lines.length - 1) * lineHeight / 2;
            
            lines.forEach((line, index) => {
                const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                text.setAttribute('class', 'node-text');
                text.setAttribute('x', node.x);
                text.setAttribute('y', startY + index * lineHeight);
                text.textContent = line;
                
                // 根据节点类型设置字体大小（增加所有字体大小）
                let fontSize;
                switch(node.type) {
                    case 'center':
                        fontSize = '20px'; // 从16px增加到20px
                        text.setAttribute('font-weight', '900');
                        text.setAttribute('fill', '#ff6b35');
                        break;
                    case 'category':
                        fontSize = '16px'; // 从14px增加到16px
                        text.setAttribute('font-weight', '800');
                        break;
                    case 'course':
                        fontSize = '13px'; // 从10px增加到13px
                        text.setAttribute('font-weight', '600'); // 稍微加粗
                        text.setAttribute('fill', '#1a202c'); // 课程按钮文字为深色
                        break;
                    default:
                        fontSize = '12px'; // 从9px增加到12px
                }
                text.setAttribute('font-size', fontSize);
                
                textGroup.appendChild(text);
            });
            
            nodeGroup.appendChild(textGroup);
            
            // 展开/收起指示器
            if (node.children && node.children.length > 0) {
                const indicator = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                indicator.setAttribute('class', 'expand-indicator');
                indicator.setAttribute('x', node.x + width / 2 - 10);
                indicator.setAttribute('y', node.y);
                indicator.textContent = node.expanded ? '−' : '+';
                indicator.style.fill = '#666';
                nodeGroup.appendChild(indicator);
                
                // 添加双击展开/折叠事件
                nodeGroup.addEventListener('dblclick', () => toggleNode(node.id));
                
                // 添加hover事件显示tooltip
                nodeGroup.addEventListener('mouseenter', (e) => showTooltip(e, node));
                nodeGroup.addEventListener('mouseleave', () => hideTooltip());
            }
            
            container.appendChild(nodeGroup);
        }

        // 获取类别对应的CSS类名
        function getCategoryClass(categoryName) {
            const categoryMap = {
                '公共必修课': 'category-public-required',
                '公共限选课': 'category-public-elective',
                '公共选修课': 'category-public-elective', 
                '专业基础课': 'category-major-basic',
                '专业核心课': 'category-major-core',
                '专业拓展课': 'category-major-expand'
            };
            return categoryMap[categoryName] || '';
        }

        // 切换节点展开/收起
        function toggleNode(nodeId) {
            const node = findNodeById(mindmapData, nodeId);
            if (node && node.children && node.children.length > 0) {
                node.expanded = !node.expanded;
                renderMindMap();
            }
        }

        // 根据ID查找节点
        function findNodeById(node, id) {
            if (node.id === id) return node;
            if (node.children) {
                for (let child of node.children) {
                    const found = findNodeById(child, id);
                    if (found) return found;
                }
            }
            return null;
        }

        // 全部展开
        function expandAll() {
            expandAllNodes(mindmapData);
            renderMindMap();
            showNotification('已展开所有类别课程', 'success');
        }

        // 全部收起
        function collapseAll() {
            collapseAllNodes(mindmapData);
            renderMindMap();
            showNotification('已收起所有类别课程', 'info');
        }

        // 递归展开所有节点
        function expandAllNodes(node) {
            if (node.children && node.children.length > 0) {
                node.expanded = true;
                node.children.forEach(child => expandAllNodes(child));
            }
        }

        // 递归收起所有节点
        function collapseAllNodes(node) {
            if (node.children && node.children.length > 0) {
                // 中心节点保持展开，只收起类别节点
                if (node.type !== 'center') {
                    node.expanded = false;
                }
                node.children.forEach(child => collapseAllNodes(child));
            }
        }

        // 缩放功能 (0.5x-2x范围)
        function zoomIn() {
            if (zoomLevel < 2) {
                zoomLevel = Math.min(zoomLevel * 1.2, 2);
                applyTransform();
                showNotification(`缩放至 ${Math.round(zoomLevel * 100)}%`, 'info');
            }
        }

        function zoomOut() {
            if (zoomLevel > 0.5) {
                zoomLevel = Math.max(zoomLevel / 1.2, 0.5);
                applyTransform();
                showNotification(`缩放至 ${Math.round(zoomLevel * 100)}%`, 'info');
            }
        }

        function resetZoom() {
            const optimalZoom = calculateOptimalZoom(document.getElementById('mindmapGroup'), 
                document.getElementById('mindmapSvg').getBoundingClientRect().width, 
                document.getElementById('mindmapSvg').getBoundingClientRect().height);
            zoomLevel = optimalZoom;
            panX = 0;
            panY = 0;
            applyTransform();
            showNotification('已重置到最佳视图', 'info');
        }

        // Tooltip功能
        let tooltipElement = null;

        function showTooltip(event, node) {
            if (node.type === 'course' && node.data) {
                if (!tooltipElement) {
                    tooltipElement = document.createElement('div');
                    tooltipElement.className = 'node-tooltip';
                    document.body.appendChild(tooltipElement);
                }
                
                const course = node.data;
                tooltipElement.innerHTML = `
                    <div style="font-weight: 700; margin-bottom: 8px;">${course.name}</div>
                    <div style="display: flex; gap: 15px; font-size: 11px;">
                        <span>🕐 ${course.hours || '—'} 学时</span>
                        <span>📚 ${course.credits || '—'} 学分</span>
                        <span>📝 ${course.exam || '—'}</span>
                    </div>
                    ${course.note ? `<div style="margin-top: 8px; font-size: 10px; opacity: 0.8;">${course.note}</div>` : ''}
                `;
                
                const rect = event.target.getBoundingClientRect();
                tooltipElement.style.left = rect.right + 10 + 'px';
                tooltipElement.style.top = rect.top - 10 + 'px';
                tooltipElement.classList.add('show');
            }
        }

        function hideTooltip() {
            if (tooltipElement) {
                tooltipElement.classList.remove('show');
            }
        }

        // 边界限制函数
        function limitPanBounds() {
            const svg = document.getElementById('mindmapSvg');
            const rect = svg.getBoundingClientRect();
            const maxPanX = rect.width * 0.3;
            const maxPanY = rect.height * 0.3;
            
            panX = Math.max(-maxPanX, Math.min(maxPanX, panX));
            panY = Math.max(-maxPanY, Math.min(maxPanY, panY));
        }

        // 应用变换
        function applyTransform() {
            const group = document.getElementById('mindmapGroup');
            group.setAttribute('transform', `translate(${panX}, ${panY}) scale(${zoomLevel})`);
        }

        // 初始化交互功能
        function initMindMapInteraction() {
            const svg = document.getElementById('mindmapSvg');
            const group = document.getElementById('mindmapGroup');
            
            // 窗口大小变化时重新渲染思维导图
            window.addEventListener('resize', () => {
                renderMindMap();
            });
            
            // 鼠标拖拽功能
            svg.addEventListener('mousedown', (e) => {
                isDragging = true;
                dragStartX = e.clientX;
                dragStartY = e.clientY;
                e.preventDefault();
            });

            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    const deltaX = e.clientX - dragStartX;
                    const deltaY = e.clientY - dragStartY;
                    
                    panX += deltaX;
                    panY += deltaY;
                    
                    dragStartX = e.clientX;
                    dragStartY = e.clientY;
                    
                    applyTransform();
                }
            });

            document.addEventListener('mouseup', () => {
                isDragging = false;
            });

            // 鼠标滚轮缩放
            svg.addEventListener('wheel', (e) => {
                e.preventDefault();
                
                const rect = svg.getBoundingClientRect();
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const zoom = e.deltaY > 0 ? 0.9 : 1.1;
                const newZoomLevel = Math.max(0.1, Math.min(3, zoomLevel * zoom));
                
                if (newZoomLevel !== zoomLevel) {
                    const zoomChange = newZoomLevel / zoomLevel;
                    panX = centerX - (centerX - panX) * zoomChange;
                    panY = centerY - (centerY - panY) * zoomChange;
                    zoomLevel = newZoomLevel;
                    
                    applyTransform();
                }
            });

            // 键盘快捷键
            document.addEventListener('keydown', (e) => {
                if (document.getElementById('mindmapModal').style.display === 'flex') {
                    switch(e.key) {
                        case 'Escape':
                            closeMindMap();
                            break;
                        case '+':
                        case '=':
                            zoomIn();
                            break;
                        case '-':
                            zoomOut();
                            break;
                        case '0':
                            resetZoom();
                            break;
                        case 'e':
                        case 'E':
                            expandAll();
                            break;
                        case 'c':
                        case 'C':
                            collapseAll();
                            break;
                    }
                }
            });
        }

        // 关闭思维导图
        function closeMindMap() {
            document.getElementById('mindmapModal').style.display = 'none';
            // 重置状态
            zoomLevel = 1;
            panX = 0;
            panY = 0;
        }
    </script>
</body>
</html> 